'use client'

import { useState, useEffect, useCallback } from 'react'
import { Search, Filter, Edit, Trash2, Download } from 'lucide-react'
import { format } from 'date-fns'
import { getTrades, searchTrades, deleteTrade, calculateRReturn } from '@/lib/database'
import { Trade, isSupabaseConfigured } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'

interface TradeLogProps {
  refreshTrigger: number
  selectedDate?: string
  onEditTrade: (trade: Trade) => void
}

export default function TradeLog({ refreshTrigger, selectedDate, onEditTrade }: TradeLogProps) {
  const { user } = useAuth()
  const [trades, setTrades] = useState<Trade[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<'entry_datetime' | 'ticker' | 'profit_loss'>('entry_datetime')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(25)

  // Advanced filters
  const [dateRangeStart, setDateRangeStart] = useState('')
  const [dateRangeEnd, setDateRangeEnd] = useState('')
  const [profitLossFilter, setProfitLossFilter] = useState<'all' | 'winners' | 'losers' | 'open'>('all')
  const [optionTypeFilter, setOptionTypeFilter] = useState<'all' | 'Call' | 'Put'>('all')
  const [dayOfWeekFilter, setDayOfWeekFilter] = useState<'all' | 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | 'Sunday'>('all')
  const [minContracts, setMinContracts] = useState('')
  const [maxContracts, setMaxContracts] = useState('')
  const [showFilters, setShowFilters] = useState(false)

  const loadTrades = useCallback(async () => {
    try {
      setLoading(true)

      // If Supabase is configured but user is not signed in, show empty data
      if (isSupabaseConfigured && !user) {
        setTrades([])
        return
      }

      const data = await getTrades(100, 0)

      // Filter by selected date if provided
      let filteredData = data
      if (selectedDate) {
        filteredData = data.filter(trade =>
          trade.entry_datetime.startsWith(selectedDate)
        )
      }

      setTrades(filteredData)
    } catch (error) {
      console.error('Error loading trades:', error)
      setTrades([])
    } finally {
      setLoading(false)
    }
  }, [selectedDate, user])

  const handleSearch = useCallback(async () => {
    if (!searchTerm.trim()) {
      loadTrades()
      return
    }

    // If Supabase is configured but user is not signed in, show empty data
    if (isSupabaseConfigured && !user) {
      setTrades([])
      return
    }

    try {
      setLoading(true)
      const data = await searchTrades(searchTerm)
      setTrades(data)
    } catch (error) {
      console.error('Error searching trades:', error)
      setTrades([])
    } finally {
      setLoading(false)
    }
  }, [searchTerm, loadTrades, user])

  useEffect(() => {
    loadTrades()
  }, [refreshTrigger, selectedDate, loadTrades])

  // Refresh trades when user authentication state changes
  useEffect(() => {
    loadTrades()
  }, [user]) // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (searchTerm) {
      handleSearch()
    } else {
      loadTrades()
    }
  }, [searchTerm, handleSearch, loadTrades])

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this trade?')) {
      return
    }

    try {
      await deleteTrade(id)
      setTrades(trades.filter(trade => trade.id !== id))
    } catch (error) {
      console.error('Error deleting trade:', error)
      alert('Error deleting trade. Please try again.')
    }
  }

  // Apply all filters
  const filteredTrades = trades.filter(trade => {
    // Search filter
    const matchesSearch = !searchTerm ||
      trade.ticker.toLowerCase().includes(searchTerm.toLowerCase())

    // Selected date filter (from calendar)
    const matchesSelectedDate = !selectedDate ||
      format(new Date(trade.entry_datetime), 'yyyy-MM-dd') === selectedDate

    // Date range filter
    const tradeDate = format(new Date(trade.entry_datetime), 'yyyy-MM-dd')
    const matchesDateRange = (!dateRangeStart || tradeDate >= dateRangeStart) &&
                            (!dateRangeEnd || tradeDate <= dateRangeEnd)

    // Profit/Loss filter
    let matchesProfitLoss = true
    if (profitLossFilter === 'winners') {
      matchesProfitLoss = trade.profit_loss !== null && trade.profit_loss > 0
    } else if (profitLossFilter === 'losers') {
      matchesProfitLoss = trade.profit_loss !== null && trade.profit_loss < 0
    } else if (profitLossFilter === 'open') {
      matchesProfitLoss = trade.profit_loss === null
    }

    // Option type filter
    const matchesOptionType = optionTypeFilter === 'all' ||
      trade.option_type === optionTypeFilter

    // Day of week filter
    const tradeDayOfWeek = format(new Date(trade.entry_datetime), 'EEEE') // Full day name (Monday, Tuesday, etc.)
    const matchesDayOfWeek = dayOfWeekFilter === 'all' || tradeDayOfWeek === dayOfWeekFilter

    // Contracts filter (only applies to option trades)
    const matchesContracts = (!minContracts || (trade.contracts !== null && trade.contracts >= parseInt(minContracts))) &&
                            (!maxContracts || (trade.contracts !== null && trade.contracts <= parseInt(maxContracts)))

    return matchesSearch && matchesSelectedDate && matchesDateRange &&
           matchesProfitLoss && matchesOptionType && matchesDayOfWeek && matchesContracts
  })

  // Sort the filtered trades
  const sortedTrades = [...filteredTrades].sort((a, b) => {
    let aValue: string | number = ''
    let bValue: string | number = ''

    if (sortBy === 'entry_datetime') {
      aValue = new Date(a[sortBy] as string).getTime()
      bValue = new Date(b[sortBy] as string).getTime()
    } else if (sortBy === 'ticker') {
      aValue = (a[sortBy] as string)?.toLowerCase() || ''
      bValue = (b[sortBy] as string)?.toLowerCase() || ''
    } else if (sortBy === 'profit_loss' || sortBy === 'percentage_return') {
      aValue = (a[sortBy] as number) || 0
      bValue = (b[sortBy] as number) || 0
    }

    if (sortOrder === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
    }
  })

  // Pagination logic
  const totalTrades = sortedTrades.length
  const totalPages = Math.ceil(totalTrades / pageSize)
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedTrades = sortedTrades.slice(startIndex, endIndex)

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [searchTerm, selectedDate, dateRangeStart, dateRangeEnd, profitLossFilter, optionTypeFilter, dayOfWeekFilter, minContracts, maxContracts])

  // Reset to first page when page size changes
  useEffect(() => {
    setCurrentPage(1)
  }, [pageSize])

  // Change sort order to ascending when a specific date is selected to show earliest trades first
  useEffect(() => {
    if (selectedDate && sortBy === 'entry_datetime') {
      setSortOrder('asc')
    } else if (!selectedDate && sortBy === 'entry_datetime') {
      setSortOrder('desc')
    }
  }, [selectedDate, sortBy])

  const formatCurrency = (amount: number | null) => {
    if (amount === null) return '-'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatDateTime = (dateTime: string) => {
    return format(new Date(dateTime), 'MMM dd, yyyy h:mm a')
  }

  // Parse date string as local date to avoid timezone issues
  const parseLocalDate = (dateString: string) => {
    const [year, month, day] = dateString.split('-').map(Number)
    return new Date(year, month - 1, day) // month is 0-indexed
  }

  const getProfitLossColor = (profitLoss: number | null) => {
    if (profitLoss === null) return 'text-gray-500 dark:text-gray-400'
    return profitLoss >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
  }

  // Calculate statistics for the currently displayed trades
  const calculateTradeStats = () => {
    const completedTrades = sortedTrades.filter(trade => trade.profit_loss !== null)

    if (completedTrades.length === 0) {
      return {
        totalTrades: sortedTrades.length,
        completedTrades: 0,
        totalPnL: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        avgWin: 0,
        avgLoss: 0,
        avgReturn: 0,
        bestTrade: 0,
        worstTrade: 0,
        totalFees: 0,
        totalRReturn: 0,
        avgRReturn: 0,
        avgMfeRReturn: 0
      }
    }

    const totalPnL = completedTrades.reduce((sum, trade) => sum + (trade.profit_loss || 0), 0)
    const winningTrades = completedTrades.filter(trade => (trade.profit_loss || 0) > 0)
    const losingTrades = completedTrades.filter(trade => (trade.profit_loss || 0) < 0)
    const totalFees = completedTrades.reduce((sum, trade) => sum + (trade.fees || 0), 0)

    const avgWin = winningTrades.length > 0
      ? winningTrades.reduce((sum, trade) => sum + (trade.profit_loss || 0), 0) / winningTrades.length
      : 0

    const avgLoss = losingTrades.length > 0
      ? losingTrades.reduce((sum, trade) => sum + (trade.profit_loss || 0), 0) / losingTrades.length
      : 0

    const avgReturn = completedTrades.length > 0
      ? completedTrades.reduce((sum, trade) => sum + (trade.percentage_return || 0), 0) / completedTrades.length
      : 0

    const pnlValues = completedTrades.map(trade => trade.profit_loss || 0)
    const bestTrade = pnlValues.length > 0 ? Math.max(...pnlValues) : 0
    const worstTrade = pnlValues.length > 0 ? Math.min(...pnlValues) : 0

    // Calculate R-return statistics (only for trades with R-return data)
    const tradesWithRReturn = completedTrades.filter(trade => trade.r_return !== null && trade.r_return !== undefined)
    const totalRReturn = tradesWithRReturn.reduce((sum, trade) => sum + (trade.r_return || 0), 0)
    const avgRReturn = tradesWithRReturn.length > 0 ? totalRReturn / tradesWithRReturn.length : 0

    // Calculate MFE R-return statistics (only for stock trades with MFE data)
    const tradesWithMfe = completedTrades.filter(trade =>
      trade.trade_type === 'stock' &&
      trade.mfe_price !== null &&
      trade.mfe_price !== undefined &&
      trade.stop_price !== null &&
      trade.direction !== null
    )
    const mfeRReturns = tradesWithMfe.map(trade =>
      calculateRReturn(trade.entry_price, trade.mfe_price!, trade.stop_price!, trade.direction!)
    ).filter(val => val !== null) as number[]
    const avgMfeRReturn = mfeRReturns.length > 0 ? mfeRReturns.reduce((sum, val) => sum + val, 0) / mfeRReturns.length : 0

    return {
      totalTrades: sortedTrades.length,
      completedTrades: completedTrades.length,
      totalPnL,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: completedTrades.length > 0 ? (winningTrades.length / completedTrades.length) * 100 : 0,
      avgWin,
      avgLoss,
      avgReturn,
      bestTrade,
      worstTrade,
      totalFees,
      totalRReturn,
      avgRReturn,
      avgMfeRReturn
    }
  }

  const stats = calculateTradeStats()

  // Clear all filters
  const clearAllFilters = () => {
    setSearchTerm('')
    setDateRangeStart('')
    setDateRangeEnd('')
    setProfitLossFilter('all')
    setOptionTypeFilter('all')
    setDayOfWeekFilter('all')
    setMinContracts('')
    setMaxContracts('')
  }

  // Check if any filters are active
  const hasActiveFilters = searchTerm || dateRangeStart || dateRangeEnd ||
    profitLossFilter !== 'all' || optionTypeFilter !== 'all' ||
    dayOfWeekFilter !== 'all' || minContracts || maxContracts

  // Export trades to CSV
  const exportToCSV = () => {
    if (sortedTrades.length === 0) {
      alert('No trades to export')
      return
    }

    // Define CSV headers
    const headers = [
      'Entry Date/Time',
      'Exit Date/Time',
      'Ticker',
      'Trade Type',
      'Option Type',
      'Strike Price',
      'Expiration Date',
      'Contracts',
      'Direction',
      'Shares',
      'Stop Price',
      'Entry Price',
      'Exit Price',
      'Fees',
      'Profit/Loss',
      'R-Return',
      'Risk Amount',
      'MFE R-Return',
      'Day of Week',
      'Created At',
      'Updated At'
    ]

    // Convert trades to CSV rows
    const csvRows = sortedTrades.map(trade => [
      format(new Date(trade.entry_datetime), 'yyyy-MM-dd HH:mm:ss'),
      trade.exit_datetime ? format(new Date(trade.exit_datetime), 'yyyy-MM-dd HH:mm:ss') : '',
      trade.ticker,
      trade.trade_type,
      trade.option_type || '',
      trade.option_strike || '',
      trade.option_expiration || '',
      trade.contracts || '',
      trade.direction || '',
      trade.shares || '',
      trade.stop_price || '',
      trade.entry_price,
      trade.exit_price || '',
      trade.fees || '',
      trade.profit_loss || '',
      trade.r_return || '',
      trade.risk_amount || '',
      trade.trade_type === 'stock' && trade.mfe_price && trade.stop_price && trade.direction
        ? calculateRReturn(trade.entry_price, trade.mfe_price, trade.stop_price, trade.direction) || ''
        : '',
      format(new Date(trade.entry_datetime), 'EEEE'),
      format(new Date(trade.created_at), 'yyyy-MM-dd HH:mm:ss'),
      format(new Date(trade.updated_at), 'yyyy-MM-dd HH:mm:ss')
    ])

    // Combine headers and rows
    const csvContent = [headers, ...csvRows]
      .map(row => row.map(field => {
        // Escape fields that contain commas, quotes, or newlines
        const stringField = String(field)
        if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n')) {
          return `"${stringField.replace(/"/g, '""')}"`
        }
        return stringField
      }).join(','))
      .join('\n')

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)

      // Generate filename with current date and filter info
      const now = new Date()
      const dateStr = format(now, 'yyyy-MM-dd')
      let filename = `trades-${dateStr}`

      if (searchTerm) filename += `-${searchTerm}`
      if (profitLossFilter !== 'all') filename += `-${profitLossFilter}`
      if (dayOfWeekFilter !== 'all') filename += `-${dayOfWeekFilter}`
      if (selectedDate) filename += `-${selectedDate}`

      link.setAttribute('download', `${filename}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Trade Log {selectedDate && `- ${format(new Date(selectedDate), 'MMM dd, yyyy')}`}
          </h2>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-4 h-4" />
            <input
              type="text"
              placeholder="Search by ticker..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full sm:w-64"
            />
          </div>
        </div>

        {/* Sort Controls and Filter Toggle */}
        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600 dark:text-gray-400">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'entry_datetime' | 'ticker' | 'profit_loss')}
              className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              title="Sort by field"
            >
              <option value="entry_datetime">Date</option>
              <option value="ticker">Ticker</option>
              <option value="profit_loss">P&L</option>
            </select>
            <button
              type="button"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
              title={`Sort ${sortOrder === 'asc' ? 'descending' : 'ascending'}`}
            >
              {sortOrder === 'asc' ? '↑' : '↓'}
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={() => setShowFilters(!showFilters)}
              className="text-sm text-blue-600 hover:text-blue-800 flex items-center space-x-1"
            >
              <Filter className="w-4 h-4" />
              <span>{showFilters ? 'Hide Filters' : 'Show Filters'}</span>
            </button>
            {hasActiveFilters && (
              <button
                type="button"
                onClick={clearAllFilters}
                className="text-sm text-red-600 hover:text-red-800"
              >
                Clear Filters
              </button>
            )}
            {sortedTrades.length > 0 && (
              <button
                type="button"
                onClick={exportToCSV}
                className="text-sm text-black-600 hover:text-black-800 flex items-center space-x-1"
                title="Export filtered trades to CSV"
              >
                <Download className="w-4 h-4" />
                <span>Export CSV</span>
              </button>
            )}
          </div>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Advanced Filters</h4>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {/* Date Range */}
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Date Range</label>
                <div className="flex space-x-2">
                  <input
                    type="date"
                    value={dateRangeStart}
                    onChange={(e) => setDateRangeStart(e.target.value)}
                    className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 flex-1 bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                    placeholder="Start"
                  />
                  <input
                    type="date"
                    value={dateRangeEnd}
                    onChange={(e) => setDateRangeEnd(e.target.value)}
                    className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 flex-1 bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                    placeholder="End"
                  />
                </div>
              </div>

              {/* Profit/Loss Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Trade Result</label>
                <select
                  value={profitLossFilter}
                  onChange={(e) => setProfitLossFilter(e.target.value as 'all' | 'winners' | 'losers' | 'open')}
                  className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 w-full bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                  title="Filter by trade result"
                >
                  <option value="all">All Trades</option>
                  <option value="winners">Winners Only</option>
                  <option value="losers">Losers Only</option>
                  <option value="open">Open Positions</option>
                </select>
              </div>

              {/* Option Type Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Option Type</label>
                <select
                  value={optionTypeFilter}
                  onChange={(e) => setOptionTypeFilter(e.target.value as 'all' | 'Call' | 'Put')}
                  className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 w-full bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                  title="Filter by option type"
                >
                  <option value="all">All Types</option>
                  <option value="Call">Calls Only</option>
                  <option value="Put">Puts Only</option>
                </select>
              </div>

              {/* Day of Week Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Day of Week</label>
                <select
                  value={dayOfWeekFilter}
                  onChange={(e) => setDayOfWeekFilter(e.target.value as 'all' | 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | 'Sunday')}
                  className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 w-full bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                  title="Filter by day of week"
                >
                  <option value="all">All Days</option>
                  <option value="Monday">Monday</option>
                  <option value="Tuesday">Tuesday</option>
                  <option value="Wednesday">Wednesday</option>
                  <option value="Thursday">Thursday</option>
                  <option value="Friday">Friday</option>
                  <option value="Saturday">Saturday</option>
                  <option value="Sunday">Sunday</option>
                </select>
              </div>

              {/* Contracts Range */}
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Contracts</label>
                <div className="flex space-x-2">
                  <input
                    type="number"
                    min="1"
                    value={minContracts}
                    onChange={(e) => setMinContracts(e.target.value)}
                    className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 flex-1 bg-white dark:bg-gray-600 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder="Min"
                  />
                  <input
                    type="number"
                    min="1"
                    value={maxContracts}
                    onChange={(e) => setMaxContracts(e.target.value)}
                    className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 flex-1 bg-white dark:bg-gray-600 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder="Max"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Trade Statistics Cards */}
      {stats.totalTrades > 0 && (
        <div className="mb-6 mx-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center">
            Statistics
            {searchTerm && ` for "${searchTerm}"`}
            {selectedDate && ` on ${format(parseLocalDate(selectedDate), 'MMM dd, yyyy')}`}
            {profitLossFilter !== 'all' && ` (${profitLossFilter})`}
            {optionTypeFilter !== 'all' && ` (${optionTypeFilter}s)`}
            {dayOfWeekFilter !== 'all' && ` (${dayOfWeekFilter}s)`}
            {(dateRangeStart || dateRangeEnd) && ` (filtered)`}
          </h3>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-9 gap-4">
            {/* Total Trades */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalTrades}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Total Trades</div>
              {stats.completedTrades !== stats.totalTrades && (
                <div className="text-xs text-gray-400 dark:text-gray-500">
                  {stats.completedTrades} completed
                </div>
              )}
            </div>

            {/* Total P&L */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${getProfitLossColor(stats.totalPnL)}`}>
                {stats.totalPnL >= 0 ? '+$' : '-$'}{Math.abs(stats.totalPnL).toFixed(2)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Total P&L</div>
            </div>

            {/* Win Rate */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${stats.winRate >= 50 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {stats.winRate.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Win Rate</div>
              <div className="text-xs text-gray-400 dark:text-gray-500">
                {stats.winningTrades}W / {stats.losingTrades}L
              </div>
            </div>

            {/* Average Return */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${getProfitLossColor(stats.avgReturn)}`}>
                {stats.avgReturn >= 0 ? '+' : ''}{stats.avgReturn.toFixed(2)}%
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Avg Return</div>
            </div>

            {/* Best Trade */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${getProfitLossColor(stats.bestTrade)}`}>
                {stats.bestTrade >= 0 ? '+$' : '-$'}{Math.abs(stats.bestTrade).toFixed(2)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Best Trade</div>
            </div>

            {/* Worst Trade */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${getProfitLossColor(stats.worstTrade)}`}>
                {stats.worstTrade >= 0 ? '+$' : '-$'}{Math.abs(stats.worstTrade).toFixed(2)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Worst Trade</div>
            </div>

            {/* Total R-Return */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${stats.totalRReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {stats.totalRReturn >= 0 ? '+' : ''}{stats.totalRReturn.toFixed(2)}R
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Total R-Return</div>
            </div>

            {/* Average R-Return */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${stats.avgRReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {stats.avgRReturn >= 0 ? '+' : ''}{stats.avgRReturn.toFixed(2)}R
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Avg R-Return</div>
            </div>

            {/* Average MFE R-Return */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${stats.avgMfeRReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {stats.avgMfeRReturn >= 0 ? '+' : ''}{stats.avgMfeRReturn.toFixed(2)}R
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Avg MFE</div>
            </div>
          </div>

          {/* Additional Stats Row */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">
            {/* Average Win */}
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
              <div className="text-xl font-bold text-green-600 dark:text-green-400">
                +${stats.avgWin.toFixed(2)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Avg Win</div>
            </div>

            {/* Average Loss */}
            <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 text-center">
              <div className="text-xl font-bold text-red-600 dark:text-red-400">
                {stats.avgLoss >= 0 ? '+$' : '-$'}{Math.abs(stats.avgLoss).toFixed(2)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Avg Loss</div>
            </div>

            {/* Total Fees */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className="text-xl font-bold text-gray-900 dark:text-white">
                ${stats.totalFees.toFixed(2)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Total Fees</div>
            </div>
          </div>
        </div>
      )}

      {/* Trade Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Entry Date/Time
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Ticker
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Type/Details
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Quantity
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Entry Price
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Exit Price
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                P&L
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                R-Return
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Risk Amount
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                MFE
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {loading ? (
              <tr>
                <td colSpan={11} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                  Loading trades...
                </td>
              </tr>
            ) : sortedTrades.length === 0 ? (
              <tr>
                <td colSpan={11} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                  {isSupabaseConfigured && !user
                    ? 'Sign in to view your trades'
                    : 'No trades found'
                  }
                </td>
              </tr>
            ) : (
              paginatedTrades.map((trade) => (
                <tr key={trade.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">
                    {formatDateTime(trade.entry_datetime)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {trade.ticker}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {trade.trade_type === 'option' ? (
                      <div>
                        <div className="text-xs font-medium text-blue-600 dark:text-blue-400 mb-1">
                          Option
                        </div>
                        <div>
                          ${trade.option_strike}
                          {trade.option_type && (
                            <span className="ml-1 text-xs font-medium text-gray-600 dark:text-gray-400">
                              {trade.option_type === 'Call' ? 'C' : 'P'}
                            </span>
                          )}
                        </div>
                        {trade.option_expiration && (
                          <div className="text-xs">
                            {format(parseLocalDate(trade.option_expiration), 'MM/dd/yy')}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div>
                        <div className="text-xs font-medium text-green-600 dark:text-green-400 mb-1">
                          Stock
                        </div>
                        <div className="text-xs">
                          {trade.direction === 'long' ? 'Long' : 'Short'}
                        </div>
                        {trade.stop_price && (
                          <div className="text-xs text-gray-500">
                            Stop: ${trade.stop_price.toFixed(2)}
                          </div>
                        )}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">
                    {trade.trade_type === 'option' ? trade.contracts : trade.shares}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">
                    {formatCurrency(trade.entry_price)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">
                    {formatCurrency(trade.exit_price)}
                  </td>
                  <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${getProfitLossColor(trade.profit_loss)}`}>
                    {formatCurrency(trade.profit_loss)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">
                    {trade.trade_type === 'stock' && trade.r_return !== null && trade.r_return !== undefined
                      ? `${trade.r_return.toFixed(2)}R`
                      : '-'
                    }
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">
                    {trade.trade_type === 'stock' && trade.risk_amount !== null && trade.risk_amount !== undefined
                      ? `$${trade.risk_amount.toFixed(2)}`
                      : '-'
                    }
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">
                    {trade.trade_type === 'stock' && trade.mfe_price && trade.stop_price && trade.direction
                      ? `${(calculateRReturn(trade.entry_price, trade.mfe_price, trade.stop_price, trade.direction) || 0).toFixed(2)}R`
                      : '-'
                    }
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={() => onEditTrade(trade)}
                        className="text-blue-600 hover:text-blue-800"
                        title="Edit trade"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        type="button"
                        onClick={() => handleDelete(trade.id)}
                        className="text-red-600 hover:text-red-800"
                        title="Delete trade"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination Controls */}
      {sortedTrades.length > 0 && (
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            {/* Page Size Selector */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700 dark:text-gray-300">Show:</span>
              <select
                value={pageSize}
                onChange={(e) => setPageSize(Number(e.target.value))}
                className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                aria-label="Select number of trades per page"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
                <option value={totalTrades}>All ({totalTrades})</option>
              </select>
              <span className="text-sm text-gray-700 dark:text-gray-300">trades per page</span>
            </div>

            {/* Pagination Info and Controls */}
            <div className="flex items-center space-x-4">
              {/* Page Info */}
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Showing {startIndex + 1}-{Math.min(endIndex, totalTrades)} of {totalTrades} trades
              </span>

              {/* Page Navigation */}
              {totalPages > 1 && (
                <div className="flex items-center space-x-1">
                  {/* Previous Button */}
                  <button
                    type="button"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  >
                    Previous
                  </button>

                  {/* Page Numbers */}
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum
                      if (totalPages <= 5) {
                        pageNum = i + 1
                      } else if (currentPage <= 3) {
                        pageNum = i + 1
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i
                      } else {
                        pageNum = currentPage - 2 + i
                      }

                      return (
                        <button
                          type="button"
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          className={`px-3 py-1 text-sm border rounded ${
                            currentPage === pageNum
                              ? 'bg-blue-600 text-white border-blue-600'
                              : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white'
                          }`}
                        >
                          {pageNum}
                        </button>
                      )
                    })}
                  </div>

                  {/* Next Button */}
                  <button
                    type="button"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  >
                    Next
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
